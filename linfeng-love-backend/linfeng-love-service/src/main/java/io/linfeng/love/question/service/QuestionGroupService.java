package io.linfeng.love.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.question.entity.QuestionGroupEntity;

import java.util.Map;

/**
 * 问题类型
 *
 * <AUTHOR>
 * @date 2023-12-21 13:34:34
 */
public interface QuestionGroupService extends IService<QuestionGroupEntity> {

    PageObject queryPage(Map<String, Object> params);
}

