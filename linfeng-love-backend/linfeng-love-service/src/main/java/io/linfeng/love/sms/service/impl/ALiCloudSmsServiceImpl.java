package io.linfeng.love.sms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.sms.service.AliCloudSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 阿里云短信
 *
 */
@Slf4j
@Component
public class ALiCloudSmsServiceImpl implements AliCloudSmsService {

    private String regionId;

    private String accessKeyId;

    private String accessKeySecret;

    private String sign;

    private String templateId;

    private String domain;

    private String action;

    private String version;


    private final ConfigSystemService configSystemService;


    public ALiCloudSmsServiceImpl(ConfigSystemService configSystemService){
        this.configSystemService = configSystemService;
        init();
    }


    @Override
    public void sendSmsCode(String mobileNo, String code) {
        DefaultProfile profile = DefaultProfile.getProfile(
                regionId,
                accessKeyId,
                accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain(domain);
        request.setVersion(version);
        request.setAction(action);
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", mobileNo);
        request.putQueryParameter("SignName", sign);
        request.putQueryParameter("TemplateCode", templateId);
        JSONObject params = new JSONObject();
        params.put("code",code);
        request.putQueryParameter("TemplateParam", params.toJSONString());
        try{
            client.getCommonResponse(request);
        }catch (Exception e){
            log.error("发送短信异常", e);
            throw new LinfengException("发送短信失败，请稍后再试");
        }

    }

    private void init(){
        regionId = configSystemService.getValue(Constant.ALI_SMS_REGION_ID);
        accessKeyId = configSystemService.getValue(Constant.ALI_SMS_KEY_ID);
        accessKeySecret = configSystemService.getValue(Constant.ALI_SMS_SECRET);
        sign = configSystemService.getValue(Constant.ALI_SMS_SIGN);
        templateId = configSystemService.getValue(Constant.ALI_SMS_TEMPLATE_ID);
        domain = configSystemService.getValue(Constant.ALI_SMS_DOMAIN);
        action = configSystemService.getValue(Constant.ALI_SMS_ACTION);
        version = configSystemService.getValue(Constant.ALI_SMS_VERSION);
    }
}
