package io.linfeng.love.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.love.question.dto.response.QuestionGroupResponseDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.question.dao.QuestionDao;
import io.linfeng.love.question.entity.QuestionEntity;
import io.linfeng.love.question.service.QuestionService;


@Service("questionService")
public class QuestionServiceImpl extends ServiceImpl<QuestionDao, QuestionEntity> implements QuestionService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<QuestionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(QuestionEntity::getSort);
        Integer groupId = Integer.parseInt((String)params.get("groupId"));
        wrapper.eq(QuestionEntity::getGroupId, groupId);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<QuestionEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

    @Override
    public List<QuestionGroupResponseDTO> getQuestionList() {
        return this.baseMapper.getQuestionList();
    }

}