package io.linfeng.love.user.dao;

import io.linfeng.love.user.dto.response.UserGiftResponseDTO;
import io.linfeng.love.user.entity.UserGiftEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户礼物记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-03 17:08:16
 */
@Mapper
public interface UserGiftDao extends BaseMapper<UserGiftEntity> {

    /**
     * 获取用户礼物列表
     * @param uid 用户id
     * @return 用户礼物列表
     */
    List<UserGiftResponseDTO> getUserGiftList(@Param("uid") Integer uid);
	
}
