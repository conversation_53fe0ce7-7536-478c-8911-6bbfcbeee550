package io.linfeng.love.question.dao;

import io.linfeng.love.question.dto.response.QuestionGroupResponseDTO;
import io.linfeng.love.question.entity.QuestionEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 问题详情
 * 
 * <AUTHOR>
 * @date 2023-12-21 13:34:34
 */
@Mapper
public interface QuestionDao extends BaseMapper<QuestionEntity> {

    List<QuestionGroupResponseDTO> getQuestionList();
	
}
