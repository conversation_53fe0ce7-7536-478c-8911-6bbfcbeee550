package io.linfeng.love.petal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.petal.entity.PetalRecommendEntity;

import java.util.Map;

/**
 * 花瓣推荐嘉宾表
 *
 * <AUTHOR>
 * @date 2023-10-31 13:49:22
 */
public interface PetalRecommendService extends IService<PetalRecommendEntity> {

    PageObject queryPage(Map<String, Object> params);
}

