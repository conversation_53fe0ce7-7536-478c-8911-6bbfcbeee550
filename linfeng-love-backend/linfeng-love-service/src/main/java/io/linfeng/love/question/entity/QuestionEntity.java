package io.linfeng.love.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 问题详情
 * 
 * <AUTHOR>
 * @date 2023-12-21 13:34:34
 */
@Data
@TableName("lf_question")
public class QuestionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 分组编号
	 */
	private Integer groupId;
	/**
	 * 链接类型
	 */
	private Integer linkType;
	/**
	 * 问题
	 */
	private String question;
	/**
	 * 回答
	 */
	private String answer;
	/**
	 * sort
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
