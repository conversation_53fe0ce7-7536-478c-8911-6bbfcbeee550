package io.linfeng.love.user.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.user.dto.response.UserIntroResponseDTO;
import io.linfeng.love.user.entity.UserIntroEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户介绍信息数据服务
 * 
 * <AUTHOR>
 * @date 2023-08-29 16:40:09
 */
@Mapper
public interface UserIntroDao extends BaseMapper<UserIntroEntity> {

    /**
     * 获取用户介绍详情
     * @param uid 用户uid
     * @param introId 介绍id
     * @return 用户介绍详情
     */
    UserIntroResponseDTO getUserIntro(@Param("uid")Integer uid, @Param("introId")Integer introId);

    /**
     * 获取用户介绍列表
     * @param uid 用户id
     * @return 用户介绍列表
     */
    List<UserIntroResponseDTO> getUserIntroList(@Param("uid")Integer uid);
	
}
