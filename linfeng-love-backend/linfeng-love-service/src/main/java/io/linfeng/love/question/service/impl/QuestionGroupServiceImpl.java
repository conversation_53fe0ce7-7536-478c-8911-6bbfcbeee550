package io.linfeng.love.question.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.question.dao.QuestionGroupDao;
import io.linfeng.love.question.entity.QuestionGroupEntity;
import io.linfeng.love.question.service.QuestionGroupService;


@Service("questionGroupService")
public class QuestionGroupServiceImpl extends ServiceImpl<QuestionGroupDao, QuestionGroupEntity> implements QuestionGroupService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<QuestionGroupEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}