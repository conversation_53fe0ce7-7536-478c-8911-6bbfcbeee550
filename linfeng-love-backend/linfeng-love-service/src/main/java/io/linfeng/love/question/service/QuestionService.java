package io.linfeng.love.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.question.dto.response.QuestionGroupResponseDTO;
import io.linfeng.love.question.entity.QuestionEntity;

import java.util.List;
import java.util.Map;

/**
 * 问题详情
 *
 * <AUTHOR>
 * @date 2023-12-21 13:34:34
 */
public interface QuestionService extends IService<QuestionEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<QuestionGroupResponseDTO> getQuestionList();

}

