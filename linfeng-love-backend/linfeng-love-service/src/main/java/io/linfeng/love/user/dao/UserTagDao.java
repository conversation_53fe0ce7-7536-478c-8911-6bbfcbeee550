package io.linfeng.love.user.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.user.dto.response.UserTagResponseDTO;
import io.linfeng.love.user.entity.UserTagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户标签表
 * 
 * <AUTHOR>
 * @date 2023-10-16 16:01:29
 */
@Mapper
public interface UserTagDao extends BaseMapper<UserTagEntity> {

    List<UserTagResponseDTO> getUserTagList(@Param("uid")Integer uid);

    void removeUserTag(@Param("uid")Integer uid, @Param("tagIdList")List<Integer> tagIdList);

}
