package io.linfeng.love.petal.service.impl;

import io.linfeng.love.petal.service.PetalRecommendService;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.petal.dao.PetalRecommendDao;
import io.linfeng.love.petal.entity.PetalRecommendEntity;


@Service("petalRecommendService")
public class PetalRecommendServiceImpl extends ServiceImpl<PetalRecommendDao, PetalRecommendEntity> implements PetalRecommendService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<PetalRecommendEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}