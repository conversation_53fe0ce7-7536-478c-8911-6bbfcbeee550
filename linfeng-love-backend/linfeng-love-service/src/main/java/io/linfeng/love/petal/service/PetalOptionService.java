/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.love.petal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.petal.entity.PetalOptionEntity;

import java.util.Map;

/**
 * 花瓣充值选项
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface PetalOptionService extends IService<PetalOptionEntity> {

    PageObject queryPage(Map<String, Object> params);

}

