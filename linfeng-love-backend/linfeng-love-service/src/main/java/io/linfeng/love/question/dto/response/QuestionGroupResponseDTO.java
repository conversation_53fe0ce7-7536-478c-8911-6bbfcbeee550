package io.linfeng.love.question.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class QuestionGroupResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 分组名称
	 */
	private String groupName;
	/**
	 * 问题列表
	 */
	private List<QuestionResponseDTO> questionList;

}
