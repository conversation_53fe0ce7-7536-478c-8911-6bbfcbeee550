package io.linfeng.love.petal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.petal.entity.PetalRecordEntity;

import java.util.Map;

/**
 * 花瓣交易记录表
 *
 * <AUTHOR>
 * @date 2023-09-15 12:15:53
 */
public interface PetalRecordService extends IService<PetalRecordEntity> {

    PageObject queryPage(Map<String, Object> params);
}

