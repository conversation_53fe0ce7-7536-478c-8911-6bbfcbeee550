package io.linfeng.love.user.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.user.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-20 12:10:43
 */
@Mapper
public interface UserDao extends BaseMapper<UserEntity> {

    List<Integer> getAllUidList();

    void clearLimitPetal();

    List<Map<String, Integer>> getRegisterCountList();
}
