package io.linfeng.love.question.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class QuestionResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 问题id
	 */
	private Integer id;
	/**
	 * 跳转类型
	 */
	private Integer linkType;
	/**
	 * 问题内容
	 */
	private String question;
	/**
	 * 回答内容
	 */
	private String answer;

}
