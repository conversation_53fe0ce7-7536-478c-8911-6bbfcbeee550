spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************
            username: root
            password: root
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true
    jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
    rabbitmq:
      host: 127.0.0.1
      port: 5672
      username: guest
      password: guest
      listener:
        simple:
          retry:
            enabled: true           # 开启消费者出现异常情况下，进行重试消费，默认false
            max-attempts: 5         # 最大重试次数，默认为3
            initial-interval: 3000  # 重试间隔时间，默认1000(单位毫秒)


logging:
  level:
    io.linfeng: DEBUG
    org.springframework.web: DEBUG
    io.swagger.*: ERROR
